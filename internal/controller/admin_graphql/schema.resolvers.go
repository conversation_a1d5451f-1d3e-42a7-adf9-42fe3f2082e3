package admin_graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.78

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin_graphql/gql_model"
)

// CreateTask is the resolver for the createTask field.
func (r *mutationResolver) CreateTask(ctx context.Context, input gql_model.CreateTaskInput) (*gql_model.ActivityTask, error) {
	panic(fmt.Errorf("not implemented: CreateTask - createTask"))
}

// UpdateTask is the resolver for the updateTask field.
func (r *mutationResolver) UpdateTask(ctx context.Context, input gql_model.UpdateTaskInput) (*gql_model.ActivityTask, error) {
	panic(fmt.Errorf("not implemented: UpdateTask - updateTask"))
}

// DeleteTask is the resolver for the deleteTask field.
func (r *mutationResolver) DeleteTask(ctx context.Context, taskID string) (bool, error) {
	panic(fmt.Errorf("not implemented: DeleteTask - deleteTask"))
}

// CreateTaskCategory is the resolver for the createTaskCategory field.
func (r *mutationResolver) CreateTaskCategory(ctx context.Context, input gql_model.CreateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	panic(fmt.Errorf("not implemented: CreateTaskCategory - createTaskCategory"))
}

// UpdateTaskCategory is the resolver for the updateTaskCategory field.
func (r *mutationResolver) UpdateTaskCategory(ctx context.Context, input gql_model.UpdateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	panic(fmt.Errorf("not implemented: UpdateTaskCategory - updateTaskCategory"))
}

// DeleteTaskCategory is the resolver for the deleteTaskCategory field.
func (r *mutationResolver) DeleteTaskCategory(ctx context.Context, categoryID string) (bool, error) {
	panic(fmt.Errorf("not implemented: DeleteTaskCategory - deleteTaskCategory"))
}

// CreateTierBenefit is the resolver for the createTierBenefit field.
func (r *mutationResolver) CreateTierBenefit(ctx context.Context, input gql_model.CreateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	panic(fmt.Errorf("not implemented: CreateTierBenefit - createTierBenefit"))
}

// UpdateTierBenefit is the resolver for the updateTierBenefit field.
func (r *mutationResolver) UpdateTierBenefit(ctx context.Context, input gql_model.UpdateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	panic(fmt.Errorf("not implemented: UpdateTierBenefit - updateTierBenefit"))
}

// DeleteTierBenefit is the resolver for the deleteTierBenefit field.
func (r *mutationResolver) DeleteTierBenefit(ctx context.Context, tierBenefitID string) (bool, error) {
	panic(fmt.Errorf("not implemented: DeleteTierBenefit - deleteTierBenefit"))
}

// AdminResetDailyTasks is the resolver for the adminResetDailyTasks field.
func (r *mutationResolver) AdminResetDailyTasks(ctx context.Context) (bool, error) {
	panic(fmt.Errorf("not implemented: AdminResetDailyTasks - adminResetDailyTasks"))
}

// AdminResetWeeklyTasks is the resolver for the adminResetWeeklyTasks field.
func (r *mutationResolver) AdminResetWeeklyTasks(ctx context.Context) (bool, error) {
	panic(fmt.Errorf("not implemented: AdminResetWeeklyTasks - adminResetWeeklyTasks"))
}

// AdminResetMonthlyTasks is the resolver for the adminResetMonthlyTasks field.
func (r *mutationResolver) AdminResetMonthlyTasks(ctx context.Context) (bool, error) {
	panic(fmt.Errorf("not implemented: AdminResetMonthlyTasks - adminResetMonthlyTasks"))
}

// AdminRecalculateAllUserTiers is the resolver for the adminRecalculateAllUserTiers field.
func (r *mutationResolver) AdminRecalculateAllUserTiers(ctx context.Context) (bool, error) {
	panic(fmt.Errorf("not implemented: AdminRecalculateAllUserTiers - adminRecalculateAllUserTiers"))
}

// AdminSeedInitialTasks is the resolver for the adminSeedInitialTasks field.
func (r *mutationResolver) AdminSeedInitialTasks(ctx context.Context) (bool, error) {
	panic(fmt.Errorf("not implemented: AdminSeedInitialTasks - adminSeedInitialTasks"))
}

// AdminGetAllTasks is the resolver for the adminGetAllTasks field.
func (r *queryResolver) AdminGetAllTasks(ctx context.Context) ([]*gql_model.ActivityTask, error) {
	panic(fmt.Errorf("not implemented: AdminGetAllTasks - adminGetAllTasks"))
}

// AdminGetTaskCompletionStats is the resolver for the adminGetTaskCompletionStats field.
func (r *queryResolver) AdminGetTaskCompletionStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminTaskCompletionStatsResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetTaskCompletionStats - adminGetTaskCompletionStats"))
}

// AdminGetUserActivityStats is the resolver for the adminGetUserActivityStats field.
func (r *queryResolver) AdminGetUserActivityStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminUserActivityStatsResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetUserActivityStats - adminGetUserActivityStats"))
}

// AdminGetTierDistribution is the resolver for the adminGetTierDistribution field.
func (r *queryResolver) AdminGetTierDistribution(ctx context.Context) (*gql_model.AdminTierDistributionResponse, error) {
	panic(fmt.Errorf("not implemented: AdminGetTierDistribution - adminGetTierDistribution"))
}

// AdminGetTopUsers is the resolver for the adminGetTopUsers field.
func (r *queryResolver) AdminGetTopUsers(ctx context.Context, limit *int) ([]*gql_model.UserTierInfo, error) {
	panic(fmt.Errorf("not implemented: AdminGetTopUsers - adminGetTopUsers"))
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
